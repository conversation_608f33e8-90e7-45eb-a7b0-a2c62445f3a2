# API测试服务

这是一个基于OpenAPI规范的API测试工具，可以自动解析`openapi.yaml`文件并生成可视化的API测试界面。

## 功能特性

- 🚀 自动解析OpenAPI 3.0规范文件
- 🎯 支持GET、POST、PUT、DELETE等HTTP方法
- 📝 智能表单生成（路径参数、查询参数、请求体）
- 🔐 支持全局Token认证配置
- 📊 实时显示请求和响应详情
- 💡 自动生成示例请求数据
- 🎨 美观的Bootstrap界面

## 快速开始

### 1. 安装依赖
```bash
npm install
```

### 2. 启动服务
```bash
npm start
```

### 3. 访问测试页面
打开浏览器访问：http://localhost:3000

## 使用说明

### 全局配置
在页面顶部配置：
- **API基础URL**: 目标API服务器的基础地址（如：http://localhost:8080）
- **认证Token**: 用于API认证的Token（会自动添加到请求头的Authorization字段）

### 测试API端点
1. 点击任意API端点的"测试"按钮展开测试表单
2. 填写必要的参数：
   - **路径参数**: URL中的动态参数（如`/user/{id}`中的id）
   - **查询参数**: URL查询字符串参数
   - **请求体**: POST/PUT请求的JSON数据
3. 点击"发送请求"按钮执行测试
4. 查看响应结果，包括状态码、响应数据等

### 支持的API端点

当前解析到的API包括：

#### 数据库相关
- `/rpc/nc/project/{title}` - 获取项目信息
- `/rpc/nc/project/showTables/{prefix}` - 显示表列表
- `/rpc/nc/project/showColumns/{table}` - 显示表字段
- `/rpc/nc/project/showDataMap` - 查询数据映射
- `/rpc/nc/project/showData` - 查询数据

#### 数据库引擎
- `/rpc/engine/db/connect/test` - 测试数据库连接
- `/rpc/engine/db/connect` - 数据库连接
- `/rpc/engine/db/getTables` - 获取数据库表
- `/rpc/engine/db/getColumns` - 获取表字段
- `/rpc/engine/db/query` - 数据查询
- `/rpc/engine/db/queryBySql` - SQL查询
- `/rpc/engine/db/export` - 数据导出

#### 用户管理
- `/user/page` - 用户分页查询
- `/user/backend/page` - 后台账号分页查询
- `/user/list` - 用户列表查询
- `/user/query/list` - 批量查询用户

#### 角色管理
- `/role` - 新增角色
- `/role/{id}` - 删除角色
- `/role/page` - 角色分页查询
- `/role/list` - 角色列表查询

#### 登录认证
- `/login/adminlogin` - 管理员登录
- `/login/logout` - 退出登录
- `/login/refresh/token` - 刷新Token
- `/login/get/token` - 获取Token

#### 菜单管理
- `/menus` - 新增菜单
- `/menus/{id}` - 删除菜单
- `/menus/page` - 菜单分页查询

#### 任务管理
- `/task/{name}/run` - 手动触发任务
- `/task/{name}/stop` - 暂停任务
- `/task/{name}/start` - 恢复任务
- `/task/status` - 获取任务状态

## 项目结构

```
├── server.js              # 主服务器文件
├── package.json           # 项目配置
├── openapi.yaml          # OpenAPI规范文件
├── views/
│   └── index.ejs         # 主页模板
├── public/
│   └── js/
│       └── api-tester.js # 前端JavaScript
└── README.md             # 说明文档
```

## 技术栈

- **后端**: Node.js + Express
- **前端**: Bootstrap 5 + Vanilla JavaScript
- **模板引擎**: EJS
- **HTTP客户端**: Axios
- **YAML解析**: js-yaml

## 开发模式

使用nodemon进行开发：
```bash
npm run dev
```

## 注意事项

1. 确保`openapi.yaml`文件在项目根目录
2. 目标API服务器需要支持CORS或在同域下
3. 某些API可能需要特定的认证方式，请根据实际情况调整Token格式
4. 复杂的请求体结构需要手动编辑JSON数据

## 故障排除

### 服务器启动失败
- 检查端口3000是否被占用
- 确认所有依赖已正确安装

### API请求失败
- 检查基础URL是否正确
- 确认目标服务器是否运行
- 检查CORS设置
- 验证Token格式是否正确

### 页面显示异常
- 检查浏览器控制台错误信息
- 确认静态资源加载正常
- 验证OpenAPI文件格式是否正确
